# AI智能出题系统

基于Vue3 + Element Plus的现代化AI试卷生成工具

## 🌟 项目特色

- **🎯 智能出题**: 通过AI API接口智能生成高质量试卷
- **🎨 现代化界面**: 美观大方的渐变设计和毛玻璃效果
- **⚙️ 灵活配置**: 支持多种题型、难度和自定义要求
- **📄 Word导出**: 生成的试卷可导出为Word文档
- **📱 响应式设计**: 完美适配各种设备屏幕

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **构建工具**: Vite
- **样式**: CSS3 (渐变、毛玻璃效果)
- **AI集成**: OpenAI API (可配置其他AI服务商)
- **文档导出**: docx.js + file-saver

## 📦 安装和运行

### 环境要求
- Node.js >= 20.19.0
- npm 或 yarn

### 安装依赖
```bash
cd ai-exam-generator
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 🔧 配置说明

### AI API配置
在 `src/services/aiService.ts` 文件中配置AI服务：

```typescript
const AI_CONFIGS = {
  openai: {
    url: 'https://api.openai.com/v1/chat/completions',
    key: 'your-openai-api-key-here',  // 替换为你的API密钥
    model: 'gpt-3.5-turbo'
  }
}
```

### 支持的AI服务商
- OpenAI (GPT-3.5/GPT-4)
- 可扩展支持其他AI服务商

## 📋 功能说明

### 试卷配置
- **科目**: 自定义科目名称
- **年级**: 指定年级
- **难度等级**: 简单/中等/困难
- **题目数量**: 1-50道题
- **题目类型**: 选择题、填空题、判断题、简答题、论述题
- **自定义要求**: 额外的出题要求

### 题目类型支持
- ✅ 选择题 (A/B/C/D选项)
- ✅ 填空题 (下划线表示空白)
- ✅ 判断题 (正确/错误)
- ✅ 简答题
- ✅ 论述题

### 导出功能
- 📄 Word文档导出 (.docx格式)
- 📝 文本文件导出 (.txt格式)
- 🖨️ 支持打印预览

## 🎨 界面预览

### 主界面特色
- 渐变背景 (蓝紫色渐变)
- 毛玻璃效果的卡片设计
- 现代化的表单组件
- 响应式布局设计

### 配置面板
- 直观的表单配置
- 实时参数验证
- 友好的错误提示

### 预览面板
- 实时试卷预览
- 格式化显示
- 一键导出功能

## 📝 使用流程

1. **配置试卷参数**
   - 填写科目和年级
   - 选择难度等级
   - 设置题目数量和类型
   - 添加自定义要求(可选)

2. **生成试卷**
   - 点击"生成试卷"按钮
   - 系统调用AI API生成试卷
   - 在预览面板查看结果

3. **导出试卷**
   - 点击"导出Word"按钮
   - 下载生成的试卷文档
   - 可直接打印使用

## 🔄 演示模式

当AI API不可用时，系统会自动切换到演示模式，生成模拟试卷内容，确保功能正常演示。

## 🚀 部署说明

### 生产环境部署
```bash
npm run build
```
构建完成后，将 `dist` 目录部署到Web服务器即可。

### 环境变量配置
建议在生产环境中使用环境变量来配置API密钥：
```bash
VITE_AI_API_KEY=your-api-key-here
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过Issue联系我们。

---

**注意**: 使用前请确保已正确配置AI API密钥，否则系统将运行在演示模式下。
