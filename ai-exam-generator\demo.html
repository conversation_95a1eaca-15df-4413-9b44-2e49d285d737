<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能出题系统 - 演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            text-align: center;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin: 0.5rem 0 0 0;
            font-weight: 300;
        }

        .main-content {
            padding: 2rem 0;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .feature-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-4px);
        }

        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .feature-desc {
            color: #666;
            line-height: 1.6;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }

        .tech-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .demo-section {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .demo-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .demo-content {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #ddd;
            color: #333;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1 class="title">
                📄 AI智能出题系统
            </h1>
            <p class="subtitle">基于Vue3 + Element Plus的现代化试卷生成工具</p>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="demo-card">
                <h2>项目概述</h2>
                <p>这是一个基于Vue3和Element Plus构建的AI智能出题系统，具有现代化的界面设计和强大的功能。</p>
                
                <div class="tech-stack">
                    <span class="tech-tag">Vue 3</span>
                    <span class="tech-tag">TypeScript</span>
                    <span class="tech-tag">Element Plus</span>
                    <span class="tech-tag">Vite</span>
                    <span class="tech-tag">AI API</span>
                    <span class="tech-tag">Word导出</span>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-title">智能出题</div>
                    <div class="feature-desc">
                        通过AI API接口，根据科目、年级、难度等参数智能生成高质量试卷，支持多种题型组合。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">现代化界面</div>
                    <div class="feature-desc">
                        采用Element Plus组件库，渐变背景和毛玻璃效果，提供美观大方的用户体验。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">⚙️</div>
                    <div class="feature-title">灵活配置</div>
                    <div class="feature-desc">
                        支持自定义科目、年级、难度、题目数量和类型，还可以添加额外的出题要求。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📄</div>
                    <div class="feature-title">Word导出</div>
                    <div class="feature-desc">
                        生成的试卷可以导出为Word文档格式，方便打印和分发，保持专业的排版效果。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">API集成</div>
                    <div class="feature-desc">
                        内置AI API接口调用，支持OpenAI等主流AI服务，可根据需要切换不同的AI提供商。
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">响应式设计</div>
                    <div class="feature-desc">
                        完全响应式设计，在桌面端、平板和手机上都能提供良好的使用体验。
                    </div>
                </div>
            </div>

            <div class="demo-card">
                <div class="demo-section">
                    <div class="demo-title">示例试卷输出：</div>
                    <div class="demo-content">五年级数学试卷

一、选择题（每题5分，共25分）

1. 下列哪个数是质数？
A. 4
B. 6
C. 7
D. 9

2. 一个长方形的长是8厘米，宽是5厘米，它的周长是多少厘米？
A. 13厘米
B. 26厘米
C. 40厘米
D. 18厘米

二、填空题（每题3分，共15分）

3. 3.5 × 4 = ________

4. 把2/5化成小数是________

三、应用题（每题10分，共20分）

5. 小明买了3支铅笔，每支1.5元，又买了2本练习本，每本2.8元。他一共花了多少钱？

参考答案：
1. C  2. B  3. 14  4. 0.4  5. 10.1元</div>
                </div>
            </div>

            <div class="demo-card">
                <h3>启动项目</h3>
                <p>要运行这个项目，请执行以下命令：</p>
                <div class="demo-section">
                    <div class="demo-content">cd ai-exam-generator
npm install
npm run dev</div>
                </div>
                <p style="margin-top: 1rem;">然后在浏览器中访问 <code>http://localhost:5173</code> 即可看到完整的应用界面。</p>
            </div>
        </div>
    </div>
</body>
</html>
