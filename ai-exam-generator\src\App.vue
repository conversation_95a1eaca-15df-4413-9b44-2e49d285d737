<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx'
import { saveAs } from 'file-saver'
import { generateExamWithAI, generateMockExam, type ExamConfig } from './services/aiService'
import {
  Download,
  Delete
} from '@element-plus/icons-vue'

// 响应式数据
const examConfig = reactive({
  subject: '',
  grade: '',
  difficulty: '中等',
  questionCount: 10,
  questionTypes: ['选择题'],
  customPrompt: ''
})

const generatedQuestions = ref('')
const isGenerating = ref(false)

// 题目类型选项
const questionTypeOptions = [
  { label: '选择题', value: '选择题' },
  { label: '填空题', value: '填空题' },
  { label: '判断题', value: '判断题' },
  { label: '简答题', value: '简答题' },
  { label: '论述题', value: '论述题' }
]

// 难度选项
const difficultyOptions = [
  { label: '简单', value: '简单' },
  { label: '中等', value: '中等' },
  { label: '困难', value: '困难' }
]

// 生成试卷
const generateExam = async () => {
  if (!examConfig.subject || !examConfig.grade) {
    ElMessage.warning('请填写科目和年级信息')
    return
  }

  if (examConfig.questionTypes.length === 0) {
    ElMessage.warning('请至少选择一种题目类型')
    return
  }

  isGenerating.value = true
  const loading = ElLoading.service({
    lock: true,
    text: '正在生成试卷...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    // 尝试调用AI API
    generatedQuestions.value = await generateExamWithAI(examConfig as ExamConfig)
    ElMessage.success('试卷生成成功！')
  } catch (error) {
    console.error('AI API调用失败，使用演示模式:', error)
    // 使用模拟生成的试卷内容（用于演示或API失败时的备用方案）
    generatedQuestions.value = generateMockExam(examConfig as ExamConfig)
    ElMessage.success('试卷生成成功！（演示模式）')
  } finally {
    isGenerating.value = false
    loading.close()
  }
}



// 导出为Word文档
const exportToWord = async () => {
  if (!generatedQuestions.value) {
    ElMessage.warning('请先生成试卷')
    return
  }

  try {
    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          new Paragraph({
            text: `${examConfig.grade}年级${examConfig.subject}试卷`,
            heading: HeadingLevel.TITLE,
            alignment: 'center'
          }),
          new Paragraph({
            text: '',
            spacing: { after: 200 }
          }),
          ...generatedQuestions.value.split('\n').map(line =>
            new Paragraph({
              children: [new TextRun(line)],
              spacing: { after: 100 }
            })
          )
        ]
      }]
    })

    const blob = await Packer.toBlob(doc)
    saveAs(blob, `${examConfig.subject}_${examConfig.grade}_试卷.docx`)

    ElMessage.success('Word文档导出成功！')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 清空内容
const clearContent = () => {
  generatedQuestions.value = ''
  ElMessage.info('内容已清空')
}
</script>

<template>
  <div class="exam-generator">
    <!-- 头部 -->
    <div class="header">
      <div class="container">
        <h1 class="title">
          📄 AI智能出题系统
        </h1>
        <p class="subtitle">基于人工智能的智能试卷生成工具</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="container">
        <el-row :gutter="24">
          <!-- 左侧配置面板 -->
          <el-col :lg="8" :md="10" :sm="24">
            <el-card class="config-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  ⚙️ <span>试卷配置</span>
                </div>
              </template>

              <el-form :model="examConfig" label-width="80px" label-position="top">
                <el-form-item label="科目">
                  <el-input
                    v-model="examConfig.subject"
                    placeholder="请输入科目名称"
                    prefix-icon="Reading"
                  />
                </el-form-item>

                <el-form-item label="年级">
                  <el-input
                    v-model="examConfig.grade"
                    placeholder="请输入年级"
                    prefix-icon="School"
                  />
                </el-form-item>

                <el-form-item label="难度等级">
                  <el-select v-model="examConfig.difficulty" style="width: 100%">
                    <el-option
                      v-for="item in difficultyOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="题目数量">
                  <el-input-number
                    v-model="examConfig.questionCount"
                    :min="1"
                    :max="50"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="题目类型">
                  <el-checkbox-group v-model="examConfig.questionTypes">
                    <el-checkbox
                      v-for="type in questionTypeOptions"
                      :key="type.value"
                      :label="type.value"
                    >
                      {{ type.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>

                <el-form-item label="自定义要求">
                  <el-input
                    v-model="examConfig.customPrompt"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入额外的出题要求（可选）"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    @click="generateExam"
                    :loading="isGenerating"
                    style="width: 100%"
                    size="large"
                  >
                    ⭐
                    {{ isGenerating ? '生成中...' : '生成试卷' }}
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>

          <!-- 右侧预览面板 -->
          <el-col :lg="16" :md="14" :sm="24">
            <el-card class="preview-card" shadow="hover">
              <template #header>
                <div class="card-header">
                  <div>
                    👁️ <span>试卷预览</span>
                  </div>
                  <div class="header-actions" v-if="generatedQuestions">
                    <el-button
                      type="success"
                      @click="exportToWord"
                      :icon="Download"
                    >
                      导出Word
                    </el-button>
                    <el-button
                      type="warning"
                      @click="clearContent"
                      :icon="Delete"
                    >
                      清空
                    </el-button>
                  </div>
                </div>
              </template>

              <div class="preview-content">
                <div v-if="!generatedQuestions" class="empty-state">
                  <div class="empty-icon">📝</div>
                  <p>请配置试卷参数并点击"生成试卷"开始</p>
                </div>
                <div v-else class="questions-content">
                  <pre class="questions-text">{{ generatedQuestions }}</pre>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<style scoped>
.exam-generator {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem 0;
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.title {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.title-icon {
  font-size: 2.5rem;
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0.5rem 0 0 0;
  font-weight: 300;
}

.main-content {
  padding: 2rem 0;
}

.config-card, .preview-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #2c3e50;
}

.card-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.preview-content {
  min-height: 400px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.questions-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
}

.questions-text {
  white-space: pre-wrap;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #2c3e50;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.25rem;
  }

  .main-content {
    padding: 1rem 0;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #2c3e50;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

:deep(.el-card__header) {
  background: rgba(102, 126, 234, 0.05);
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-checkbox) {
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}
</style>
