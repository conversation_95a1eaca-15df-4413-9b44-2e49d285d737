// AI服务配置
export interface ExamConfig {
  subject: string
  grade: string
  difficulty: string
  questionCount: number
  questionTypes: string[]
  customPrompt: string
}

// AI API配置 - 这里可以配置不同的AI服务提供商
const AI_CONFIGS = {
  openai: {
    url: 'https://api.openai.com/v1/chat/completions',
    key: 'your-openai-api-key-here',
    model: 'gpt-3.5-turbo'
  },
  // 可以添加其他AI服务商配置
  // anthropic: {
  //   url: 'https://api.anthropic.com/v1/messages',
  //   key: 'your-anthropic-api-key-here',
  //   model: 'claude-3-sonnet-20240229'
  // }
}

// 当前使用的AI服务
const CURRENT_AI = 'openai'

/**
 * 构建AI提示词
 */
export function buildPrompt(config: ExamConfig): string {
  const { subject, grade, difficulty, questionCount, questionTypes, customPrompt } = config
  
  const basePrompt = `请为${grade}年级的${subject}科目生成一份${difficulty}难度的试卷。

要求：
- 题目数量：${questionCount}道
- 题目类型：${questionTypes.join('、')}
- 难度等级：${difficulty}

请按照以下格式输出：
1. 试卷标题
2. 每道题目要有明确的题号
3. 选择题需要提供4个选项（A、B、C、D）
4. 填空题用下划线表示空白处
5. 判断题用（）表示判断符号
6. 在试卷最后提供参考答案

${customPrompt ? `额外要求：${customPrompt}` : ''}

请确保题目内容准确、有教育意义，符合该年级学生的认知水平。`

  return basePrompt
}

/**
 * 调用AI API生成试卷
 */
export async function generateExamWithAI(config: ExamConfig): Promise<string> {
  const aiConfig = AI_CONFIGS[CURRENT_AI]
  const prompt = buildPrompt(config)

  try {
    const response = await fetch(aiConfig.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${aiConfig.key}`
      },
      body: JSON.stringify({
        model: aiConfig.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的教育工作者和出题专家，擅长根据不同年级和科目的特点生成高质量的试卷。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.7
      })
    })

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.choices[0].message.content
  } catch (error) {
    console.error('AI API调用失败:', error)
    throw error
  }
}

/**
 * 生成模拟试卷（用于演示或API失败时的备用方案）
 */
export function generateMockExam(config: ExamConfig): string {
  const { subject, grade, difficulty, questionCount, questionTypes } = config
  
  let examContent = `${grade}年级${subject}试卷\n\n`
  
  let questionNumber = 1
  
  // 生成不同类型的题目
  questionTypes.forEach(type => {
    const typeCount = Math.ceil(questionCount / questionTypes.length)
    
    switch (type) {
      case '选择题':
        examContent += `一、选择题（每题5分，共${typeCount * 5}分）\n\n`
        for (let i = 0; i < typeCount && questionNumber <= questionCount; i++) {
          examContent += `${questionNumber}. 关于${subject}的基础知识，下列说法正确的是？\n`
          examContent += `A. 选项A的内容\n`
          examContent += `B. 选项B的内容\n`
          examContent += `C. 选项C的内容\n`
          examContent += `D. 选项D的内容\n\n`
          questionNumber++
        }
        break
        
      case '填空题':
        examContent += `二、填空题（每题3分，共${typeCount * 3}分）\n\n`
        for (let i = 0; i < typeCount && questionNumber <= questionCount; i++) {
          examContent += `${questionNumber}. ${subject}的重要概念是________，它的特点包括________。\n\n`
          questionNumber++
        }
        break
        
      case '判断题':
        examContent += `三、判断题（每题2分，共${typeCount * 2}分）\n\n`
        for (let i = 0; i < typeCount && questionNumber <= questionCount; i++) {
          examContent += `${questionNumber}. ${subject}的某个重要理论是正确的。（  ）\n\n`
          questionNumber++
        }
        break
        
      case '简答题':
        examContent += `四、简答题（每题10分，共${typeCount * 10}分）\n\n`
        for (let i = 0; i < typeCount && questionNumber <= questionCount; i++) {
          examContent += `${questionNumber}. 请简述${subject}中的重要概念及其应用。\n\n`
          questionNumber++
        }
        break
        
      case '论述题':
        examContent += `五、论述题（每题15分，共${typeCount * 15}分）\n\n`
        for (let i = 0; i < typeCount && questionNumber <= questionCount; i++) {
          examContent += `${questionNumber}. 请详细论述${subject}的发展历程及其对现代社会的影响。\n\n`
          questionNumber++
        }
        break
    }
  })
  
  // 添加参考答案
  examContent += `\n参考答案：\n`
  examContent += `（此处为演示内容，实际使用时会调用AI API生成真实试卷和答案）\n`
  examContent += `选择题：1.C 2.B 3.A...\n`
  examContent += `填空题：1.概念名称，特点描述...\n`
  examContent += `判断题：1.√ 2.× 3.√...\n`
  
  return examContent
}
